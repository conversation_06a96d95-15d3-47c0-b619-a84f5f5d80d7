import type { FlatConfig } from '@typescript-eslint/utils/ts-eslint';
/**
 * Recommended rules for code correctness that you can drop in without additional configuration.
 * @see {@link https://typescript-eslint.io/users/configs#recommended}
 */
declare const _default: (plugin: FlatConfig.Plugin, parser: FlatConfig.Parser) => FlatConfig.ConfigArray;
export default _default;
//# sourceMappingURL=recommended.d.ts.map