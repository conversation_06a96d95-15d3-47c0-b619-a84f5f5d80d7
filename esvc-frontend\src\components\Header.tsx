import React, { useState } from 'react';
import '../styles/components/Header.css';
import logoEsvc from '../assets/logo-esvc.png';

const Header: React.FC = () => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  return (
    <header className="header">
      <div className="container">
        <div className="header-content">
          {/* Logo */}
          <div className="logo">
            <img src={logoEsvc} alt="ESVC Logo" className="logo-image" />
          </div>

          {/* Desktop Navigation */}
          <nav className="nav desktop-nav">
            <a href="#home" className="nav-link active">Home</a>
            <a href="#stake" className="nav-link">Stake ESVC</a>
            <a href="#funding" className="nav-link">Get Funding</a>
            <a href="#challenge" className="nav-link">Trade Challenge</a>
            <a href="#contact" className="nav-link">Contact Us</a>
          </nav>

          {/* Desktop Action Buttons */}
          <div className="header-actions desktop-actions">
            <button className="btn-secondary">Login</button>
            <button className="btn-primary">Get Started</button>
          </div>

          {/* Mobile Hamburger Menu */}
          <button className="mobile-menu-toggle" onClick={toggleMobileMenu}>
            <span className="hamburger-line"></span>
            <span className="hamburger-line"></span>
            <span className="hamburger-line"></span>
          </button>
        </div>

        {/* Mobile Menu */}
        <div className={`mobile-menu ${isMobileMenuOpen ? 'mobile-menu-open' : ''}`}>
          <nav className="mobile-nav">
            <a href="#home" className="mobile-nav-link">Home</a>
            <a href="#stake" className="mobile-nav-link">Stake ESVC</a>
            <a href="#funding" className="mobile-nav-link">Get Funding</a>
            <a href="#challenge" className="mobile-nav-link">Trade Challenge</a>
            <a href="#contact" className="mobile-nav-link">Contact Us</a>
          </nav>
          <div className="mobile-actions">
            <button className="btn-primary mobile-btn">Get Started</button>
            <button className="mobile-login">Login</button>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
