import React from 'react';
import '../styles/components/Hero.css';

const Hero: React.FC = () => {
  return (
    <section className="hero">
      <div className="container">
        <div className="hero-content">
          {/* Floating Coins */}
          <div className="floating-coins">
            <div className="coin coin-1">
              <span className="coin-text">囲</span>
            </div>
            <div className="coin coin-2">
              <span className="coin-text">囲</span>
            </div>
            <div className="coin coin-3">
              <span className="coin-text">囲</span>
            </div>
            <div className="coin coin-4">
              <span className="coin-text">囲</span>
            </div>
          </div>

          {/* Earn Badge */}
          <div className="earn-badge">
            <span>Earn 20% annual returns</span>
          </div>

          {/* Main Content */}
          <div className="hero-text">
            <h1 className="hero-title">
              <span className="title-grow">Grow</span>{' '}
              <span className="title-wealth">Your Wealth.</span>{' '}
              <span className="title-fund">Fund</span>{' '}
              <span className="title-future">the Future.</span>
            </h1>
            <h2 className="hero-subtitle">
              <span className="subtitle-stake">Stake with</span>{' '}
              <span className="subtitle-confidence">Confidence.</span>
            </h2>
            
            <p className="hero-description">
              Stake your ESVC tokens securely, earn daily ROI, and unlock exclusive 
              opportunities to pitch your startup ideas for funding. Transparent treasury 
              tracking, secure crypto deposits, and tiered rewards.
            </p>

            <button className="btn-primary hero-cta">
              Start Staking Now
              <span className="cta-icon">🚀</span>
            </button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;
