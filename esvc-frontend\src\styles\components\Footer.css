.footer {
  background: var(--bg-primary);
  border-top: 1px solid var(--border-color);
  padding: 60px 0 20px;
}

.footer-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 40px;
  flex-wrap: wrap;
  gap: 32px;
}

/* Footer Logo */
.footer-logo .logo {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 24px;
  font-weight: 700;
  color: var(--text-primary);
}

.footer-logo .logo-icon {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, var(--accent-orange), #ff6b5b);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
  font-weight: bold;
}

.footer-logo .logo-text {
  font-family: serif;
}

.footer-logo .logo-brand {
  font-size: 28px;
  letter-spacing: 2px;
}

/* Footer Navigation */
.footer-nav {
  display: flex;
  align-items: center;
  gap: 32px;
  flex-wrap: wrap;
}

.footer-link {
  color: var(--text-secondary);
  font-weight: 500;
  transition: color 0.3s ease;
}

.footer-link:hover {
  color: var(--text-primary);
}

/* Social Media */
.footer-social {
  display: flex;
  align-items: center;
  gap: 16px;
}

.social-link {
  width: 40px;
  height: 40px;
  border: 1px solid var(--border-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
  transition: all 0.3s ease;
}

.social-link:hover {
  border-color: var(--accent-orange);
  color: var(--accent-orange);
  transform: translateY(-2px);
}

/* Footer Bottom */
.footer-bottom {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid var(--border-color);
}

.copyright {
  color: var(--text-secondary);
  font-size: 14px;
  margin: 0;
}

/* Responsive */
@media (max-width: 768px) {
  .footer-content {
    flex-direction: column;
    text-align: center;
    gap: 24px;
  }
  
  .footer-nav {
    justify-content: center;
    gap: 24px;
  }
  
  .footer-social {
    justify-content: center;
  }
  
  .footer-logo .logo-brand {
    font-size: 24px;
  }
}

@media (max-width: 480px) {
  .footer-nav {
    flex-direction: column;
    gap: 16px;
  }
}
